package net.armcloud.paascenter.openapi.netpadv2.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.config.HarborConfig;
import net.armcloud.paascenter.cms.manager.HarborConfigManage;
import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailImageSuccMapper;
import net.armcloud.paascenter.cms.model.request.*;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO;
import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
import net.armcloud.paascenter.common.core.constant.device.PadAllocationStatusConstants;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.helper.NetStoragePadHelper;
import net.armcloud.paascenter.openapi.manager.AdiCertificateManager;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.model.dto.PadDetailsDTO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.dto.*;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.PadDelRequestDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.PadOffRequestDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.PadBootOnRequestDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.base.TaskBaseRequestDTO;
import net.armcloud.paascenter.openapi.netpadv2.entity.*;
import net.armcloud.paascenter.openapi.netpadv2.factory.NetPadV2InitDataFactory;
import net.armcloud.paascenter.openapi.netpadv2.mapper.*;
import net.armcloud.paascenter.openapi.netpadv2.service.*;
import net.armcloud.paascenter.openapi.netpadv2.utils.SnowflakeIdGeneratorV3;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ResultVO;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.openapi.service.INetStorageResOffLogService;
import net.armcloud.paascenter.openapi.mapper.DevicePadMapper;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import net.armcloud.paascenter.openapi.netpadv2.factory.NetPadV2Factory;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.openapi.service.IPadStatusService;
import net.armcloud.paascenter.openapi.service.netstorage.impl.NetStorageComputeUnitServiceImpl;
import net.armcloud.paascenter.openapi.utils.AndroidDeviceInfoUtils;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.service.impl.TaskService;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2CreateParamsVO;
import net.armcloud.paascenter.openapi.netpadv2.vo.ParamCompareResultVO;
import org.apache.commons.lang3.StringUtils;
import net.armcloud.paascenter.openapi.netpadv2.validator.NetPadV2CreateValidator;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

import static net.armcloud.paascenter.common.core.constant.Constants.*;
import static net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;

/**
 * 网存实例V2服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Slf4j
@Service
public class NetPadV2ServiceImpl implements NetPadV2Service {

    @Resource
    private NetPadV2CreateValidator validator;

    @Resource
    private NetPadV2QueryService queryService;

    @Resource
    private NetPadV2CreateRecordMapper recordMapper;

    @Resource
    private PadMapper padMapper;

    @Resource
    private NetStorageResUnitService netStorageResUnitService;

    @Resource
    private PadStatusMapper padStatusMapper;

    @Resource
    private PadRoomMapper padRoomMapper;

    @Resource
    private NetStoragePadUnitDetailMapper netStoragePadUnitDetailMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private NetPadComputeUnitRelationMapper computeRelationMapper;

    @Resource
    private NetPadStorageRelationMapper storageRelationMapper;

    @Resource
    private NetPadV2Factory netPadV2Factory;

    @Resource
    private CommonPadTaskComponent padTaskComponent;

    @Resource
    private NetPadV2ComputeMatchService computeMatchService;

    @Resource
    private ArmServerMapper armServerMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private DevicePadMapper devicePadMapper;

    @Resource
    private ScreenLayoutMapper screenLayoutMapper;
    @Resource
    private RealPhoneTemplateMapper realPhoneTemplateMapper;

    @Resource
    private INetStorageResOffLogService netStorageResOffLogService;

    @Resource
    private PadMacManager padMacManager;

    private final static String TASK_REQUEST_CACHE_KEY_PREFIX = "netpadv2:task:request";

    @Resource
    private CustomerUploadImageMapper customerUploadImageMapper;

    @Resource
    private AdiCertificateManager adiCertificateManager;

    @Resource
    private HarborConfigManage harborConfigManage;

    @Resource
    private TaskService taskService;

    @Resource
    private NetStoragePadHelper netStoragePadHelper;

    @Value("${android-prop.armcloud-server-addr}")
    public String armcloudServerAddr;

    @Resource
    private TaskRelInstanceDetailImageSuccMapper taskRelInstanceDetailImageSuccMapper;

    @Resource
    private RedisService redisService;

    private final static String padCodeBootOnCntKey = "netpadv2:booton:cnt";

    @Resource
    private IPadStatusService padStatusService;

    @Resource
    private PadTaskMapper padTaskMapper;

    @Value("${spring.profiles.active:unknown}")
    private String springProfilesActive;

    @Resource
    private NetPadLastOnParamService netPadLastOnParamService;

    @Resource
    private NetStorageComputeUnitServiceImpl netStorageComputeUnitService;

    @Resource
    private NetPadStorageRelationMapper netPadStorageRelationMapper;
    @Autowired
    private IEdgeClusterConfigurationService iEdgeClusterConfigurationService;
    @Autowired
    private InstanceDetailImageSuccService instanceDetailImageSuccService;

    @Override
    public List<NetPadV2CreateResponseDTO> createNetStorageInstances(NetPadV2CreateDTO dto, SourceTargetEnum sourceTarget) {
        log.info("开始创建网存实例V2，参数：{}", dto);

        validator.validateCreateParams(dto);

        // 1. 基本参数校验
        validator.validateStorageCapacity(dto.getStorageSize());

        // 2. 校验资源规格
        ResourceSpecification resourceSpecification = queryService.getResourceSpecificationByCode(dto.getSpecificationCode());
        validator.validateResourceSpecification(resourceSpecification);

        // 3. 校验网络存储资源
        NetStorageRes storageRes = queryService.getNetStorageResByCustomerAndCluster(dto.getCustomerId(), dto.getClusterCode());
        validator.validateNetStorageRes(storageRes);

        // 4. 校验镜像
        CustomerUploadImage customerUploadImage = queryService.getCustomerUploadImageByIdAndCustomer(dto.getImageId(), dto.getCustomerId(), sourceTarget);
        validator.validateCustomerUploadImage(customerUploadImage);

        // 5. 处理ADI模板
        if (dto.getRandomADITemplates() || Objects.nonNull(dto.getRealPhoneTemplateId())) {
            RealPhoneTemplate realPhoneTemplate = handleRealPhoneTemplate(dto, customerUploadImage);
            validator.validateRealPhoneTemplate(realPhoneTemplate);
            dto.setRealPhoneTemplateId(realPhoneTemplate.getId());
            dto.setScreenLayoutCode(realPhoneTemplate.getScreenLayoutCode());
        }

        // 6. 校验屏幕布局
        ScreenLayout screenLayout = getScreenLayout(dto.getScreenLayoutCode(), dto.getCustomerId(), sourceTarget);
        validator.validateScreenLayout(screenLayout);

        // 7. 获取推流类型
        Integer streamType = queryService.getStreamTypeByCustomerId(dto.getCustomerId());

        // 8. 批量创建实例
        List<NetPadV2CreateResponseDTO> results = createInstances(dto, resourceSpecification, screenLayout, streamType, dto.getCountryCode());

        log.info("网存实例V2创建完成，共创建{}个实例", results.size());
        return results;
    }

    /**
     * 批量创建实例
     */
    private List<NetPadV2CreateResponseDTO> createInstances(NetPadV2CreateDTO dto,
                                                           ResourceSpecification resourceSpecification,
                                                           ScreenLayout screenLayout,
                                                           Integer streamType,
                                                           String countryCode) {
        List<NetPadV2CreateResponseDTO> results = new ArrayList<>();
        List<NetPadV2CreateRecord> records = new ArrayList<>();
        // 预生成所有需要的数据
        List<InstanceCreationData> creationDataList = new ArrayList<>();
        for (int i = 1; i <= dto.getNumber(); i++) {
            InstanceCreationData data = new InstanceCreationData();
            data.padCode = generateNetPadCode();
            data.netStorageResUnitCode = generateNetStorageResUnitCode(data.padCode);
            data.dns = getDnsConfig(dto);
            data.padSn = i;
            data.businessSnowflakeId = SnowflakeIdGeneratorV3.nextIdLong(); // 业务雪花算法ID
            creationDataList.add(data);
            NetPadV2CreateRecord record = createSimpleRecord(data.padCode);
            records.add(record);
        }
        List<Pad> padsToInsert = new ArrayList<>();
        List<NetStorageResUnit> unitsToInsert = new ArrayList<>();
        List<PadStatus> statusesToInsert = new ArrayList<>();
        List<PadRoom> roomsToInsert = new ArrayList<>();
        List<NetStoragePadUnitDetail> detailsToInsert = new ArrayList<>();
        List<NetPadResUnitRelationDO> storageRelations = new ArrayList<>();
        // 批量创建实体对象
        for (InstanceCreationData data : creationDataList) {
            // 创建Pad实例
            Pad pad = NetPadV2Factory.createPad(dto, resourceSpecification, screenLayout,
                    data.padCode, data.netStorageResUnitCode, data.padSn, data.dns, streamType, countryCode);
            setMacInfo(pad);
            padsToInsert.add(pad);

            // 创建网存单元
            NetStorageResUnit unit = NetPadV2Factory.createNetStorageResUnit(dto, data.padCode,
                    data.netStorageResUnitCode,
                    dto.getStorageSize().longValue());
            unitsToInsert.add(unit);

            // 创建实例状态记录
            PadStatus padStatus = createPadStatusEntity(data.padCode);
            statusesToInsert.add(padStatus);

            // 创建单控房间
            PadRoom crmRoom = createPadRoomEntity(data.padCode, 1, CRM, dto.getOprBy());
            roomsToInsert.add(crmRoom);

            // 创建群控房间
            PadRoom srmRoom = createPadRoomEntity(data.padCode, 2, SRM, dto.getOprBy());
            roomsToInsert.add(srmRoom);

            NetPadResUnitRelationDO resUnitRelation = NetPadV2Factory.createResUnitRelation(data.padCode, data.netStorageResUnitCode, dto.getOprBy());
            storageRelations.add(resUnitRelation);

            // 创建网存实例详情
            NetStoragePadUnitDetail unitDetail = createNetStoragePadUnitDetailEntity(
                    dto, pad, data.netStorageResUnitCode);
            detailsToInsert.add(unitDetail);
        }

        List<NetPadV2CreateResponseDTO> createRespList = saveDataWithTransaction(padsToInsert, unitsToInsert, statusesToInsert, roomsToInsert, detailsToInsert, storageRelations);

        if (createRespList != null) {
            results.addAll(createRespList);
        }

        // 批量保存记录（在事务外执行，避免影响主业务）
        if (!records.isEmpty()) {
            try {
                // 创建记录标识2.0实例
                recordMapper.batchInsert(records);
            } catch (Exception e) {
                log.error("保存创建记录失败：{}", e.getMessage(), e);
            }
        }

        return results;
    }

    private void setMacInfo(Pad pad) {
        String mac = padMacManager.getMac();
        pad.setMac(mac);
    }

    @Nullable
    private List<NetPadV2CreateResponseDTO> saveDataWithTransaction(List<Pad> padsToInsert,
                                                                    List<NetStorageResUnit> unitsToInsert,
                                                                    List<PadStatus> statusesToInsert,
                                                                    List<PadRoom> roomsToInsert,
                                                                    List<NetStoragePadUnitDetail> detailsToInsert,
                                                                    List<NetPadResUnitRelationDO> storageRelations) {
        return transactionTemplate.execute(status -> {
            List<NetPadV2CreateResponseDTO> txResults = new ArrayList<>();
            try {
                // 批量插入数据库
                batchInsertPads(padsToInsert);
                batchInsertNetStorageResUnits(unitsToInsert);
                batchInsertPadStatuses(statusesToInsert);
                batchInsertPadRooms(roomsToInsert);
                batchInsertNetStoragePadUnitDetails(detailsToInsert);
                batchInsertStorageRelations(storageRelations);

                // 创建响应DTO
                for (Pad pad : padsToInsert) {
                    NetPadV2CreateResponseDTO response = NetPadV2Factory.createResponseDTO(pad);
                    txResults.add(response);
                }

                log.info("批量创建实例成功，共创建{}个实例", txResults.size());
                return txResults;

            } catch (Exception e) {
                log.error("批量创建实例失败：{}", e.getMessage(), e);
                throw e;
            }
        });
    }

    @Override
    public NetPadV2ResultVO batchBootOn(NetPadV2BatchBootOnDTO param, SourceTargetEnum sourceTargetEnum) {
        log.info("批量开机网存实例V2请求，参数：{}", param);

        // 参数校验
        validator.validateBatchBootOnParams(param);
        NetPadV2ResultVO result = new NetPadV2ResultVO();

        List<String> padCodes = new ArrayList<>(param.getPadCodes());

        // 1. 查询实例详情并校验
        List<PadDetailsVO> detailsVOList = getPadDetails(padCodes);
        validateInstancesAndHandleFailures(detailsVOList, padCodes, param.getCustomerId(), result);
        if (padCodes.isEmpty()) {
            return result;
        }

        // 移除未通过校验的实例
        detailsVOList.removeIf(detailsVO -> !padCodes.contains(detailsVO.getPadCode()));
        if (CollectionUtils.isEmpty(detailsVOList)) {
            log.error("批量开机实例V2失败，所有实例均未通过校验,padCodes:{}", padCodes);
            return result;
        }

        List<String> padOffLockPadCodes = new ArrayList<>();
        List<String> padOnLockPadCodes = new ArrayList<>();
        try {

            // 批量锁定本次开机的全部实例1分钟，避免重复开关机。方法执行完成后释放。
            lockInstancesAndHandleFailures(padCodes, TaskTypeConstants.NET_PAD_ON, TaskTypeConstants.NET_PAD_ON, result);
            if (padCodes.isEmpty()) {
                return result;
            }
            padOnLockPadCodes = new ArrayList<>(padCodes);
            lockInstancesAndHandleFailures(padCodes, TaskTypeConstants.NET_PAD_OFF, TaskTypeConstants.NET_PAD_ON, result);
            if (padCodes.isEmpty()) {
                return result;
            }
            padOffLockPadCodes = new ArrayList<>(padCodes);
            // 区分首次开机与非首次开机实例
            List<String> nonFirstBootOnPadCodes = getNonFirstBootOnPadCodes(padCodes);

            transactionTemplate.execute(status -> {
                try {
                    boolean bootOnParamChanged = compareAndUpdateInstanceParams(param, detailsVOList);
                    // 参数如果发生变更，则需要重新获取padDetails。
                    List<PadDetailsVO> padDetailsVOListNew = new ArrayList<>(detailsVOList);
                    if (bootOnParamChanged) {
                        // 参数发生更新，重新获取padDetails
                        padDetailsVOListNew = getPadDetails(padCodes);
                    }
                    // 非首次开机实例校验算力
                    if (!nonFirstBootOnPadCodes.isEmpty()) {
                        Map<ExceptionCode, List<String>> computeUnitExistFailMap = validateComputeUnitExist(nonFirstBootOnPadCodes);
                        result.setFailList(computeUnitExistFailMap);
                        if (result.getFailList().size() == param.getPadCodes().size()) {
                            // 全部失败，回滚事务。
                            status.setRollbackOnly();
                            return false;
                        }
                        padCodes.removeAll(result.getFailCodeList());
                    }

                    // 分配算力、保存关联关系、创建开机任务参数
                    Pair<List<PadBootOnRequestDTO>, Map<ExceptionCode, List<String>>> bootOnRequestDTOSAndFailMap = matchComputeAndCreateBootOnRequest(param, padDetailsVOListNew);
                    Map<ExceptionCode, List<String>> matchFailMap = bootOnRequestDTOSAndFailMap.getRight();
                    result.setFailList(matchFailMap);
                    if (result.getFailList().size() == param.getPadCodes().size()) {
                        // 全部失败，回滚事务。
                        status.setRollbackOnly();
                        return false;
                    }
                    padCodes.removeAll(result.getFailCodeList());
                    List<PadBootOnRequestDTO> bootOnRequestDTOS = dealBootOnRequestDTO(param.getTimeout(), bootOnRequestDTOSAndFailMap.getLeft());
                    // 将任务参数写入缓存，用于开机时调用
                    cacheTaskRequest(bootOnRequestDTOS, NET_PAD_ON);
                    // 添加开机任务
                    PadTaskBO padTaskBO = addPadTask(param, sourceTargetEnum, bootOnRequestDTOS);

                    log.info("批量开机网存实例V2完成，任务数量：{}", padTaskBO.getSubTasks().size());
                    // 修改实例状态为开机中
                    padStatusService.updatePadStatusAndSendPadStatusCallback(padCodes, PadStatusConstant.ON_RUN, param.getCustomerId(), "batchBootOn");
                    result.setSuccessList(padTaskBO);
                    return true;
                } catch (Exception e) {
                    log.error("批量开机网存实例V2失败", e);
                    status.setRollbackOnly();
                    if (e instanceof BasicException) {
                        log.error("批量开机网存实例V2失败, BasicException:{}", e.getMessage());
                        throw e;
                    } else {
                        throw new BasicException(PadExceptionCode.NET_PAD_BOOT_ON_EXCEPTION);
                    }
                }
            });
        } finally {
            unlockPadCodes(padOnLockPadCodes, TaskTypeConstants.NET_PAD_ON);
            unlockPadCodes(padOffLockPadCodes, NET_PAD_OFF);
        }
        return result;
    }

    private static List<PadBootOnRequestDTO> dealBootOnRequestDTO(Integer timeout, List<PadBootOnRequestDTO> newBootOnRequestDTOS) {
        if (CollectionUtils.isEmpty(newBootOnRequestDTOS)) {
            throw new BasicException(PadExceptionCode.NET_PAD_BOOT_ON_EXCEPTION);
        }
        List<PadBootOnRequestDTO> bootOnRequestDTOS = new ArrayList<>(newBootOnRequestDTOS);
        bootOnRequestDTOS.forEach(bootOnRequestDTO -> {
            bootOnRequestDTO.setTimeout(timeout);
        });
        return bootOnRequestDTOS;
    }

    private List<String> getNonFirstBootOnPadCodes(List<String> padCodes) {
        List<TaskRelInstanceDetailImageSucc> taskRelInstanceDetailImageSuccList = taskRelInstanceDetailImageSuccMapper.selectList(new QueryWrapper<>(TaskRelInstanceDetailImageSucc.class).select("instance_name")
                .in("instance_name", padCodes)
                .eq("task_type", 213)
                .orderByDesc("id"));
        return taskRelInstanceDetailImageSuccList.stream().map(TaskRelInstanceDetailImageSucc::getInstanceName).collect(Collectors.toList());
    }

    /**
     * 校验实例并处理失败情况
     */
    private void validateInstancesAndHandleFailures(List<PadDetailsVO> detailsVOList, List<String> padCodes,
                                                    Long customerId, NetPadV2ResultVO result) {
        // 通用校验
        Map<ExceptionCode, List<String>> validateFailMap = validatePadListCommon(detailsVOList, padCodes, customerId);
        if (!validateFailMap.isEmpty()) {
            result.setFailList(validateFailMap);
            padCodes.removeAll(result.getFailCodeList());
        }

        // 状态校验
        if (!padCodes.isEmpty()) {
            validateFailMap = validatePadONStatus(detailsVOList);
            if (!validateFailMap.isEmpty()) {
                result.setFailList(validateFailMap);
                padCodes.removeAll(result.getFailCodeList());
            }
        }

    }

    /**
     * 锁定实例并处理失败情况
     */
    private void lockInstancesAndHandleFailures(List<String> padCodes, TaskTypeConstants lockTaskType, TaskTypeConstants optTaskType, NetPadV2ResultVO result) {
        // 锁定NET_PAD_ON
        Map<ExceptionCode, List<String>> lockFailMap = lockPadCodes(padCodes, lockTaskType, optTaskType);
        if (!lockFailMap.isEmpty()) {
            result.setFailList(lockFailMap);
            padCodes.removeAll(result.getFailCodeList());
        }
    }

    private Map<ExceptionCode, List<String>> validateComputeUnitExist(List<String> padCodes) {
        Map<ExceptionCode, List<String>> resultMap = new HashMap<>();
        // 校验算力是否存在，存在则不允许开机
        List<NetPadComputeUnitRelationDO> computeUnitRelationDOList = computeRelationMapper.selectByPadCodes(padCodes);
        if (!CollectionUtils.isEmpty(computeUnitRelationDOList)) {
            resultMap.put(PadExceptionCode.COMPUTE_UNIT_NOT_RELEASED, computeUnitRelationDOList.stream().map(NetPadComputeUnitRelationDO::getPadCode).collect(Collectors.toList()));
        }
        return resultMap;
    }

    private Map<ExceptionCode, List<String>> validateComputeUnitNotExist(List<String> padCodes) {
        Map<ExceptionCode, List<String>> resultMap = new HashMap<>();
        // 只有存在算力时可以关机
        List<NetPadComputeUnitRelationDO> computeUnitRelationDOList = computeRelationMapper.selectByPadCodes(padCodes);
        if (CollectionUtils.isEmpty(computeUnitRelationDOList) || !Objects.equals(padCodes.size(), computeUnitRelationDOList.size())) {
            List<String> computeExistsPadCodeList = computeUnitRelationDOList.stream().map(NetPadComputeUnitRelationDO::getPadCode).collect(Collectors.toList());
            for (String padCode : padCodes) {
                if (!computeExistsPadCodeList.contains(padCode)) {
                    resultMap.computeIfAbsent(PadExceptionCode.COMPUTE_UNIT_NOT_EXIST, k -> new ArrayList<>()).add(padCode);
                }
            }
        }
        return resultMap;
    }

    private void unlockPadCodes(List<String> padCodes, TaskTypeConstants taskType) {
        // 移除计数key
        for (String padCode : padCodes) {
            String cntKey = getLockCntKey(taskType, padCode);
            redisService.deleteObject(cntKey);
            String lockTaskKey = getLockTaskKey(taskType, padCode);
            redisService.deleteObject(lockTaskKey);
        }
    }

    @NotNull
    private static String getLockCntKey(TaskTypeConstants taskType, String padCode) {
        return RedisKeyUtils.counterKey(padCodeBootOnCntKey, taskType.name(), padCode);
    }

    /**
     * @param padCodes 需要锁定的实例
     * @param taskType 需要锁定的实例的操作
     * @param optTask 操作锁定的任务类型
     * @return
     */
    private Map<ExceptionCode, List<String>> lockPadCodes(List<String> padCodes, TaskTypeConstants taskType, TaskTypeConstants optTask) {
        Map<ExceptionCode, List<String>> resultMap = new HashMap<>();
        // 使用计数器进行1分钟计数
        for (String padCode : padCodes) {
            String cntKey = getLockCntKey(taskType, padCode);
            Integer increment = redisService.increment(cntKey);
            redisService.expire(cntKey, 1, TimeUnit.MINUTES);
            if (increment > 1) {
                Object v = redisService.getCacheObject(getLockTaskKey(taskType, padCode));
                if (Objects.isNull(v)) {
                    continue;
                }
                Integer optTaskType = Integer.valueOf(v.toString());
                TaskTypeConstants currOptTask = TaskTypeConstants.fromCode(optTaskType);
                if (Objects.isNull(currOptTask)) {
                    continue;
                }
                switch (currOptTask) {
                    case NET_PAD_ON:
                        resultMap.computeIfAbsent(PadExceptionCode.PAD_CODE_ON_LOCKED, k -> new ArrayList<>()).add(padCode);
                        break;
                    case NET_PAD_OFF:
                        resultMap.computeIfAbsent(PadExceptionCode.PAD_CODE_OFF_LOCKED, k -> new ArrayList<>()).add(padCode);
                        break;
                    case NET_PAD_DEL:
                        resultMap.computeIfAbsent(PadExceptionCode.PAD_CODE_DEL_LOCKED, k -> new ArrayList<>()).add(padCode);
                        break;
                }
            } else {
                // 记录当前持有锁的任务
                redisService.setCacheObject(getLockTaskKey(taskType, padCode), String.valueOf(optTask.getType()), 1L, TimeUnit.MINUTES);
            }
        }
        return resultMap;
    }

    private String getLockTaskKey(TaskTypeConstants taskType, String padCode) {
        return RedisKeyUtils.dataKey(padCodeBootOnCntKey, taskType.name(), padCode);
    }

    /**
     * 匹配算力并创建开机任务参数
     */
    private Pair<List<PadBootOnRequestDTO>, Map<ExceptionCode, List<String>>> matchComputeAndCreateBootOnRequest(NetPadV2BatchBootOnDTO param, List<PadDetailsVO> detailsVOS) {
        // 匹配算力
        List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> allNetPadRelationList = matchComputeUnit(param, detailsVOS);
        // 将匹配失败的实例转换为失败的结构，并筛选出匹配成功实例
        Map<ExceptionCode, List<String>> failMap = new HashMap<>();
        allNetPadRelationList.removeIf(relation -> {
            if (!relation.isMatchSuccess()) {
                failMap.computeIfAbsent(relation.getMatchFailException(), k -> new ArrayList<>()).add(relation.getPadCode());
                return true;
            }
            return false;
        });
        if (allNetPadRelationList.isEmpty()) {
            return Pair.of(Collections.emptyList(), failMap);
        }
        // 批量保存关联关系并返回组装的任务参数
        List<PadBootOnRequestDTO> padBootOnRequestDTOS = batchSaveRelations(allNetPadRelationList, param, detailsVOS);
        // 组装响应体 left：开机任务参数 right：失败实例
        return Pair.of(padBootOnRequestDTOS, failMap);
    }

    private PadTaskBO addPadTask(NetPadV2BatchBootOnDTO param, SourceTargetEnum sourceTargetEnum, List<PadBootOnRequestDTO> bootOnRequestDTOS) {
        List<String> padCodes = bootOnRequestDTOS.stream().map(PadBootOnRequestDTO::getPadCode).collect(Collectors.toList());
        PadTaskBO padTaskBO = createBootOnTasks(param, padCodes, JSON.toJSONString(bootOnRequestDTOS), sourceTargetEnum);
        Map<String, PadBootOnRequestDTO> requestParamMap = bootOnRequestDTOS.stream().collect(Collectors.toMap(PadBootOnRequestDTO::getPadCode, o -> o));
        padTaskBO.getSubTasks().forEach(taskBO -> {
            // FIXME 兼容历史的本地实例接口（升级镜像、一键新机、更换ADI模板、修改实例属性等）
            compatible(taskBO, requestParamMap);
        });
        return padTaskBO;
    }

    @NotNull
    private List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> matchComputeUnit(NetPadV2BatchBootOnDTO param, List<PadDetailsVO> detailsVOList) {
        // 2. 按集群分组进行算力匹配
        Map<String, List<PadDetailsVO>> clusterGroupMap = detailsVOList.stream()
                .collect(Collectors.groupingBy(PadDetailsVO::getClusterCode));

        List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> allNetPadRelationList = new ArrayList<>();

        // 3. 分集群处理算力匹配和任务创建
        for (Map.Entry<String, List<PadDetailsVO>> entry : clusterGroupMap.entrySet()) {
            String clusterCode = entry.getKey();
            List<PadDetailsVO> clusterPads = entry.getValue();

            // 分配算力与IP
            List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> netStorageDTOList = computeMatchService.batchMatchComputeAndStorage(
                    clusterCode, clusterPads, param.getCustomerId());

            allNetPadRelationList.addAll(netStorageDTOList);
        }
        return allNetPadRelationList;
    }

    private void compatible(PadTaskBO.PadSubTaskBO taskBO, Map<String, PadBootOnRequestDTO> requestParamMap) {
        // TODO 兼容本地实例历史逻辑
        try {
            PadBootOnRequestDTO requestDTO = requestParamMap.get(taskBO.getPadCode());
            ResourceSpecification resourceSpecification = netStoragePadHelper.getResourceSpecification(requestDTO.getDeviceLevel());
            VirtualizeDeviceRequest.Device data = new VirtualizeDeviceRequest.Device();
            data.setDeviceIp(requestDTO.getDeviceIp());
            VirtualizeDeviceRequest.InitInformation initInformation = new VirtualizeDeviceRequest.InitInformation();
            initInformation.setHostStorageSize(requestDTO.getResourceLimit().getHostStorageSize());
            initInformation.setContainerConcurrentSize(resourceSpecification.getPadNumber());
            data.setInitInformation(initInformation);
            List<VirtualizeDeviceRequest.Device.Pad> pads = new ArrayList<>();
            VirtualizeDeviceRequest.Device.Pad padVirtualize = new VirtualizeDeviceRequest.Device.Pad();
            padVirtualize.setPadCode(requestDTO.getPadCode());
            padVirtualize.setNetStorageResId(requestDTO.getExtraSettings().getStorageId());

            ImageRequest image = new ImageRequest();
            image.setId(requestDTO.getImage().getImageId());
            image.setTag(requestDTO.getImage().getTag());
            padVirtualize.setImage(image);

            DisplayRequest display = new DisplayRequest();
            display.setWidth(requestDTO.getAndroidBootParams().getWidth());
            display.setHeight(requestDTO.getAndroidBootParams().getHeight());
            display.setDpi(requestDTO.getAndroidBootParams().getDpi());
            display.setFps(requestDTO.getAndroidBootParams().getFps());
            padVirtualize.setDisplay(display);

            SpecRequest spec = new SpecRequest();
            spec.setCpu(requestDTO.getResourceLimit().getCpu());
            spec.setMemory(requestDTO.getResourceLimit().getMemory());
            spec.setDisk(requestDTO.getResourceLimit().getStorage());
            spec.setIsolateDisk(true);
            padVirtualize.setSpec(spec);

            NetworkRequest network = new NetworkRequest();
            //获取IP
            network.setIp(requestDTO.getContainerNetwork().getIp());
            network.setSubnet(requestDTO.getContainerNetwork().getSubnet());
            network.setIpRange(requestDTO.getContainerNetwork().getIpRange());
            network.setGateway(requestDTO.getContainerNetwork().getGateway());
            network.setNetworkDeviceName(requestDTO.getContainerNetwork().getMacvlanName());
            network.setMac(requestDTO.getContainerNetwork().getMac());
            network.setDns(requestDTO.getAndroidBootParams().getDns1());

            VirtualizeDeviceRequest.ADI adi = new VirtualizeDeviceRequest.ADI();
            adi.setTemplateUrl(requestDTO.getExtraSettings().getAdi().getDownloadUrlOfADI());
            adi.setTemplatePassword(requestDTO.getExtraSettings().getAdi().getPasswordOfADI());
            adi.setRealPhoneTemplateId(requestDTO.getExtraSettings().getAdi().getRealTemplateId());
            adi.setAndroidCertData(requestDTO.getExtraSettings().getAdi().getAndroidCertData());
            padVirtualize.setAdi(adi);

            padVirtualize.setNetwork(network);
            padVirtualize.setAndroidProp("ro.boot.armcloud_server_addr=" + armcloudServerAddr);

            pads.add(padVirtualize);
            initInformation.setContainerConcurrentSize(1);

            Map<String, String> deviceAndroidProps = new HashMap<>();
            deviceAndroidProps.put("persist.sys.cloud.madb_enable","0");
            //创建实例默认关闭adb 需要追加安卓属性
            padVirtualize.setDeviceAndroidProps(deviceAndroidProps);
            pads.add(padVirtualize);
            data.setPads(pads);
            VirtualizeDeviceRequest req = new VirtualizeDeviceRequest();
            req.setDevices(Collections.singletonList(data));
            req.setNetStorageResFlag(1);
            //写入开机任务记录
            VirtualizeDeviceInfoVO deviceInfoVO = new VirtualizeDeviceInfoVO();
            deviceInfoVO.setClusterCode(requestDTO.getClusterCode());
            taskService.saveDeviceInstance(taskBO.getMasterTaskId(), taskBO.getSubTaskId(), TaskTypeEnum.INSTANCE_NET_WORK_ON, req.getDevices().get(0), deviceInfoVO, true);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private <T extends TaskBaseRequestDTO> void cacheTaskRequest(List<T> bootOnRequestDTOS, TaskTypeConstants taskType) {
        if (CollectionUtils.isEmpty(bootOnRequestDTOS)) {
            return;
        }
        for (T t : bootOnRequestDTOS) {
            if (Objects.isNull(t)) {
                continue;
            }
            String padCode = t.getPadCode();
            String dataKey = getTaskRequestDataKey(taskType, padCode);
            redisService.setCacheObject(dataKey, t.toJSONString(), 60L, TimeUnit.MINUTES);
        }
    }

    @NotNull
    private static String getTaskRequestDataKey(TaskTypeConstants taskType, String padCode) {
        return RedisKeyUtils.dataKey(TASK_REQUEST_CACHE_KEY_PREFIX, taskType.name(), padCode);
    }

    @Override
    public JSONObject getTaskRequest(String padCode, TaskTypeConstants taskType) {
        String dataKey = getTaskRequestDataKey(taskType, padCode);
        Object v = redisService.getCacheObject(dataKey);
        if (Objects.isNull(v)) {
            return null;
        }
        String json = v.toString();
        if (StringUtils.isNotBlank(json)) {
            return JSONObject.parseObject(json);
        }
        return null;
    }

    /**
     * 批量保存关联关系，并返回组装好的任务json参数
     */
    private List<PadBootOnRequestDTO> batchSaveRelations(List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> netPadRelationList, NetPadV2BatchBootOnDTO dto, List<PadDetailsVO> detailsVOList) {
        // 获取必要数据 - 使用一次遍历替代多个stream流操作
        Set<Long> armServerIdSet = new HashSet<>();
        Set<String> deviceCodeSet = new HashSet<>();

        // 一次遍历netPadRelationList，收集armServerId和deviceCode
        for (NetPadV2ComputeMatchServiceImpl.NetPadRelation relation : netPadRelationList) {
            armServerIdSet.add(relation.getArmServerId());
            deviceCodeSet.add(relation.getDeviceCode());
        }

        Set<String> screenLayoutCodeSet = new HashSet<>();
        Set<Long> realPhoneTemplateIdSet = new HashSet<>();
        Set<String> imageIdSet = new HashSet<>();

        // 一次遍历detailsVOList，收集screenLayoutCode、realPhoneTemplateId和imageId
        for (PadDetailsVO detailsVO : detailsVOList) {
            screenLayoutCodeSet.add(detailsVO.getScreenLayoutCode());
            realPhoneTemplateIdSet.add(detailsVO.getRealPhoneTemplateId());
            imageIdSet.add(detailsVO.getImageId());
        }

        // 转换为List进行后续查询
        List<Long> armServerIdList = new ArrayList<>(armServerIdSet);
        List<String> deviceCodeList = new ArrayList<>(deviceCodeSet);
        List<String> screenLayoutCodeList = new ArrayList<>(screenLayoutCodeSet);
        List<Long> realPhoneTemplateIdList = new ArrayList<>(realPhoneTemplateIdSet);
        List<String> imageIdList = new ArrayList<>(imageIdSet);

        // 获取ArmServer相关的网络信息
        List<ArmServerNetworkInfoDTO> armServerNetworkInfoList = armServerMapper.getNetworkInfo(armServerIdList);
        // 获取板卡相关信息
        List<DeviceModelInfoDTO> deviceModelInfoList = deviceMapper.getDeviceModelInfo(deviceCodeList);
        // 获取全部ScreenLayout
        LambdaQueryWrapper<ScreenLayout> lqw = new LambdaQueryWrapper<ScreenLayout>().in(ScreenLayout::getCode, screenLayoutCodeList);
        List<ScreenLayout> screenLayoutList = screenLayoutMapper.selectList(lqw);
        // 获取ADI模板
        LambdaQueryWrapper<RealPhoneTemplate> lqw2 = new LambdaQueryWrapper<RealPhoneTemplate>().in(RealPhoneTemplate::getId, realPhoneTemplateIdList);
        List<RealPhoneTemplate> realPhoneTemplateList = realPhoneTemplateMapper.selectList(lqw2);
        // 获取实例对应的全部镜像
        List<CustomerUploadImageParameterDTO> imageList = customerUploadImageMapper.getImageParameterByImageUniqueIdList(imageIdList);

        // 初始化实例运行的必要数据
        return initData(netPadRelationList, armServerNetworkInfoList, deviceModelInfoList, screenLayoutList, realPhoneTemplateList, imageList, dto, detailsVOList);
    }

    /**
     * 初始化实例运行的必要数据（写入数据库），并组装返回开机任务需要的参数
     */
    private List<PadBootOnRequestDTO> initData(
            List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> netPadRelationList,
            List<ArmServerNetworkInfoDTO> armServerNetworkInfoList,
            List<DeviceModelInfoDTO> deviceModelInfoList,
            List<ScreenLayout> screenLayoutList,
            List<RealPhoneTemplate> realPhoneTemplateList,
            List<CustomerUploadImageParameterDTO> imageList,
            NetPadV2BatchBootOnDTO dto,
            List<PadDetailsVO> detailsVOList) {

        log.info("开始初始化实例运行数据，关系数量：{}", netPadRelationList.size());

        // 将List转换为Map以便快速查找
        Map<Long, ArmServerNetworkInfoDTO> armServerNetworkMap = armServerNetworkInfoList.stream()
                .collect(Collectors.toMap(ArmServerNetworkInfoDTO::getArmServerId, Function.identity()));

        Map<String, DeviceModelInfoDTO> deviceModelMap = deviceModelInfoList.stream()
                .collect(Collectors.toMap(DeviceModelInfoDTO::getDeviceCode, Function.identity()));

        Map<String, PadDetailsVO> padDetailsMap = detailsVOList.stream()
                .collect(Collectors.toMap(PadDetailsVO::getPadCode, Function.identity()));

        Map<String, ScreenLayout> screenLayoutMap = screenLayoutList.stream()
                .collect(Collectors.toMap(ScreenLayout::getCode, Function.identity()));

        Map<Long, RealPhoneTemplate> realPhoneTemplateMap = realPhoneTemplateList.stream()
                .collect(Collectors.toMap(RealPhoneTemplate::getId, Function.identity()));

        Map<String, CustomerUploadImageParameterDTO> imageMap = imageList.stream()
                .collect(Collectors.toMap(CustomerUploadImageParameterDTO::getImageId, Function.identity()));

        // 使用工厂类批量创建和处理数据

        // 批量创建BootOnRequestDTO
        List<PadBootOnRequestDTO> bootOnRequests = new ArrayList<>();
        List<DevicePad> devicePadList = new ArrayList<>();
        List<NetStorageResOffLog> netStorageResOffLogList = new ArrayList<>();
        List<Pad> padUpdateList = new ArrayList<>();
        List<NetPadComputeUnitRelationDO> computeRelationList = new ArrayList<>();

        for (NetPadV2ComputeMatchServiceImpl.NetPadRelation netPadRelation : netPadRelationList) {
            String padCode = netPadRelation.getPadCode();
            PadDetailsVO padDetailsVO = padDetailsMap.get(padCode);

            if (padDetailsVO == null) {
                log.warn("未找到padCode对应的详情信息：{}", padCode);
                throw new BasicException(String.format(PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getMsg(), "实例"), PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getStatus());
            }

            ArmServerNetworkInfoDTO armServerNetwork = armServerNetworkMap.get(netPadRelation.getArmServerId());
            if (armServerNetwork == null) {
                log.warn("未找到对应的网络信息，padCode：{}", padCode);
                throw new BasicException(String.format(PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getMsg(), "服务器网络"), PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getStatus());
            }
            DeviceModelInfoDTO deviceModel = deviceModelMap.get(netPadRelation.getDeviceCode());
            if (deviceModel == null) {
                log.warn("未找到对应的设备信息，padCode：{}", padCode);
                throw new BasicException(String.format(PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getMsg(), "板卡信息"), PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getStatus());
            }
            ScreenLayout screenLayout = screenLayoutMap.get(padDetailsVO.getScreenLayoutCode());
            if (screenLayout == null) {
                log.warn("未找到对应的屏幕布局信息，padCode：{}", padCode);
                throw new BasicException(String.format(PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getMsg(), "屏幕布局"), PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getStatus());
            }
            RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMap.get(padDetailsVO.getRealPhoneTemplateId());
            /*if (realPhoneTemplate == null) {
                log.warn("未找到对应的ADI模板信息，padCode：{}", padCode);
                throw new BasicException(String.format(PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getMsg(), "ADI模板"), PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getStatus());
            }*/

            CustomerUploadImageParameterDTO image = imageMap.get(padDetailsVO.getImageId());
            if (image == null) {
                log.warn("未找到对应的镜像信息，padCode：{}", padCode);
                throw new BasicException(String.format(PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getMsg(), "镜像信息"), PadExceptionCode.NET_PAD_BOOT_ON_FAILED.getStatus());
            }

            AdiCertificateRepository adiCertificateRepository = adiCertificateManager.useAdiCertificate(padCode, image.getImageParameter());

            // 创建BootOnRequestDTO
            PadBootOnRequestDTO bootOnRequest = createBootOnRequestDTO(
                    padDetailsVO,
                    armServerNetwork,
                    deviceModel,
                    screenLayout,
                    realPhoneTemplate,
                    netPadRelation,
                    dto.getCountryCode(),
                    dto.getAndroidProp(),
                    adiCertificateRepository);
            bootOnRequests.add(bootOnRequest);

            // 创建其他相关实体
            createRelatedEntities(padDetailsVO, netPadRelation, deviceModel, armServerNetwork,
                    devicePadList, netStorageResOffLogList, padUpdateList, computeRelationList);
        }

        // 批量保存数据
        batchSaveInitData(devicePadList, netStorageResOffLogList, padUpdateList, computeRelationList);

        log.info("初始化实例运行数据完成，处理数量：{}", bootOnRequests.size());
        return bootOnRequests;
    }

    /**
     * 创建BootOnRequestDTO
     */
    private PadBootOnRequestDTO createBootOnRequestDTO(
            PadDetailsVO padDetailsVO,
            ArmServerNetworkInfoDTO armServerNetwork,
            DeviceModelInfoDTO deviceModel,
            ScreenLayout screenLayout,
            RealPhoneTemplate realPhoneTemplate,
            NetPadV2ComputeMatchServiceImpl.NetPadRelation netPadRelation,
            String countryCode,
            JSONObject androidProp,
            AdiCertificateRepository adiCertificateRepository) {

        TaskRelInstanceDetail lastInfo = instanceDetailImageSuccService.getLastInfo(padDetailsVO.getPadCode());


        PadBootOnRequestDTO bootOnRequest = new PadBootOnRequestDTO();
        bootOnRequest.setPadCode(padDetailsVO.getPadCode());
        bootOnRequest.setType(padDetailsVO.getType());
        bootOnRequest.setNetStorageResFlag(1);
        bootOnRequest.setDeviceIp(deviceModel.getDeviceIp());
        bootOnRequest.setArmServerCode(armServerNetwork.getArmServerCode());
        bootOnRequest.setDeviceLevel(deviceModel.getDeviceLevel());
        bootOnRequest.setClusterCode(padDetailsVO.getClusterCode());

        // 设置容器网络配置
        PadBootOnRequestDTO.ContainerNetwork containerNetwork = new PadBootOnRequestDTO.ContainerNetwork();
        containerNetwork.setDeviceName(armServerNetwork.getMacVlan());
        containerNetwork.setGateway(armServerNetwork.getGateway());
        containerNetwork.setIpRange(armServerNetwork.getIpRange());
        containerNetwork.setSubnet(armServerNetwork.getSubnet());
        containerNetwork.setIp(netPadRelation.getIp());
        containerNetwork.setMac(padDetailsVO.getMac());
        containerNetwork.setMacvlanName(armServerNetwork.getMacVlan());
        bootOnRequest.setContainerNetwork(containerNetwork);

        // 设置资源限制配置
        PadBootOnRequestDTO.ResourceLimit resourceLimit = new PadBootOnRequestDTO.ResourceLimit();
        resourceLimit.setCpu(!Objects.isNull(padDetailsVO.getCpu()) && padDetailsVO.getCpu() > 0
                ? padDetailsVO.getCpu() : deviceModel.getCpu());
        resourceLimit.setMemory(!Objects.isNull(padDetailsVO.getMemory())
                ? padDetailsVO.getMemory() : deviceModel.getMemory());
        resourceLimit.setStorage(padDetailsVO.getNetStorageResSize().intValue());
        resourceLimit.setHostStorageSize(deviceModel.getHostStorageSize());
        bootOnRequest.setResourceLimit(resourceLimit);

        PadBootOnRequestDTO.AndroidBootParams androidBootParams = new PadBootOnRequestDTO.AndroidBootParams();
        androidBootParams.setWidth(screenLayout.getScreenWidth().intValue());
        androidBootParams.setHeight(screenLayout.getScreenHigh().intValue());
        androidBootParams.setDpi(screenLayout.getPixelDensity().intValue());
        androidBootParams.setFps(screenLayout.getScreenRefreshRate().intValue());
        String[] dnsArr = splitDns(padDetailsVO.getDns(), padDetailsVO.getClusterCode());
        androidBootParams.setDns1(dnsArr[0]);
        if (dnsArr.length > 1) {
            androidBootParams.setDns2(dnsArr[1]);
        }
        bootOnRequest.setAndroidBootParams(androidBootParams);

        // extraSettings
        PadBootOnRequestDTO.ExtraSettings extraSettings = new PadBootOnRequestDTO.ExtraSettings();
        extraSettings.setStorageId(padDetailsVO.getNetStorageResId());
        extraSettings.setStorageSize(padDetailsVO.getNetStorageResSize());
        PadBootOnRequestDTO.ExtraSettings.Adi adi = new PadBootOnRequestDTO.ExtraSettings.Adi();
        if (adiCertificateRepository != null) {
            adi.setAndroidCertData(adiCertificateRepository.getCertificate());
        }
        if (Objects.nonNull(realPhoneTemplate)) {
            adi.setDownloadUrlOfADI(realPhoneTemplate.getAdiTemplateDownloadUrl());
            adi.setPasswordOfADI(realPhoneTemplate.getAdiTemplatePwd());
            adi.setRealTemplateId(realPhoneTemplate.getId());
        }
        extraSettings.setAdi(adi);
        bootOnRequest.setExtraSettings(extraSettings);

        HarborConfig harborConfig = harborConfigManage.get();

        PadBootOnRequestDTO.Image imageConfig = new PadBootOnRequestDTO.Image();
        imageConfig.setTag("latest");
        imageConfig.setRepository(harborConfig.getUrl());
        imageConfig.setImageId(padDetailsVO.getImageId());
        bootOnRequest.setImage(imageConfig);

        // 设置安卓属性
        JSONObject androidProps = new JSONObject();
        androidProps.put("persist.sys.cloud.madb_enable","0");
        if(StringUtils.isNotEmpty(countryCode)){
            // 随机不同国家的对应信息
            AndroidDeviceInfoUtils.ramdomSimAndGPSInfo(countryCode, androidProps);
            AndroidDeviceInfoUtils.ramdomBatteryInfo(countryCode, androidProps);
            AndroidDeviceInfoUtils.ramdomWifiInfo(countryCode, androidProps);
            AndroidDeviceInfoUtils.ramdomBluetoothInfo(countryCode, androidProps);
        }
        if (Objects.nonNull(androidProp) && !androidProp.isEmpty()) {
            androidProps.putAll(androidProp);
        }
        bootOnRequest.setAndroidProps(androidProps);

        if(Objects.nonNull(lastInfo)){
            if(!lastInfo.getTaskType().equals(TaskTypeEnum.INSTANCE_UPGRADE_IMAGE.getIntValue())
                    && !lastInfo.getTaskType().equals(TaskTypeEnum.INSTANCE_REPLACE_PROP.getIntValue())){
                bootOnRequest.getResourceLimit().setCpu(lastInfo.getCpu());
                bootOnRequest.getResourceLimit().setMemory(lastInfo.getMemory());
                bootOnRequest.getAndroidBootParams().setWidth(lastInfo.getWidth());
                bootOnRequest.getAndroidBootParams().setHeight(lastInfo.getHeight());
                bootOnRequest.getAndroidBootParams().setFps(lastInfo.getFps());
                bootOnRequest.getAndroidBootParams().setDpi(lastInfo.getDpi());
            }
            bootOnRequest.setPadCode(lastInfo.getInstanceName());
            bootOnRequest.getImage().setTag(lastInfo.getImageTag());

            JSONObject deviceAndroidPropMap = new JSONObject();
            deviceAndroidPropMap.putAll(bootOnRequest.getAndroidProps());
            if (StringUtils.isNotBlank(lastInfo.getDeviceAndroidProp())) {
                deviceAndroidPropMap.putAll(Optional.ofNullable(com.alibaba.fastjson.JSON.parseObject(lastInfo.getDeviceAndroidProp())).orElse(new JSONObject()));
            }
            //每次实例开机,默认关闭adb。放到最后防止被覆盖
            deviceAndroidPropMap.put("persist.sys.cloud.madb_enable","0");
            bootOnRequest.setAndroidProps(deviceAndroidPropMap);
        }
        bootOnRequest.setAndroidProps(androidProps);

        return bootOnRequest;
    }

    private String[] splitDns(String dns,String clusterCode){
        if(StringUtils.isEmpty(dns)){
            dns = iEdgeClusterConfigurationService.getEdgeClusterConfigurationByKey(clusterCode, EdgeClusterConfigurationEnum.CLUSTER_DNS_DEFAULT_SERVERS);
        }
        return dns.split(",");
    }

    /**
     * 创建相关实体对象
     */
    private void createRelatedEntities(
            PadDetailsVO padDetailsVO,
            NetPadV2ComputeMatchServiceImpl.NetPadRelation netPadRelation,
            DeviceModelInfoDTO deviceModel,
            ArmServerNetworkInfoDTO armServerNetwork,
            List<DevicePad> devicePadList,
            List<NetStorageResOffLog> netStorageResOffLogList,
            List<Pad> padUpdateList,
            List<NetPadComputeUnitRelationDO> computeRelationList) {

        NetPadV2InitDataFactory initDataFactory = new NetPadV2InitDataFactory();

        // 创建DevicePad实体
        Long deviceId = deviceModel.getDeviceId();
        DevicePad devicePad = initDataFactory.createDevicePad(deviceId, padDetailsVO.getPadId());
        devicePadList.add(devicePad);

        // 创建NetStorageResOffLog实体
        NetStorageResOffLog storageResOffLog = initDataFactory.createNetStorageResOffLog(
                padDetailsVO, deviceModel.getDeviceIp());
        netStorageResOffLogList.add(storageResOffLog);

        // 创建Pad更新实体（用于更新IP等信息）
        Pad padUpdate = initDataFactory.createPadForIpUpdate(
                padDetailsVO.getPadId(),
                padDetailsVO.getPadCode(),
                netPadRelation.getIp(),
                armServerNetwork.getArmServerCode());
        padUpdateList.add(padUpdate);

        // 创建算力关联关系
        NetPadComputeUnitRelationDO computeRelation = netPadV2Factory.createComputeRelation(
                padDetailsVO.getPadCode(), netPadRelation.getComputeUnitCode(), "system");
        computeRelationList.add(computeRelation);
    }

    /**
     * 批量保存初始化数据
     */
    private void batchSaveInitData(
            List<DevicePad> devicePadList,
            List<NetStorageResOffLog> netStorageResOffLogList,
            List<Pad> padUpdateList,
            List<NetPadComputeUnitRelationDO> computeRelationList) {

        // 使用事务模板确保数据一致性
        transactionTemplate.execute(status -> {
            try {
                // 批量插入DevicePad关联关系
                if (!devicePadList.isEmpty()) {
                    devicePadMapper.batchInsert(devicePadList);
                    log.info("批量插入DevicePad关联关系，数量：{}", devicePadList.size());
                }

                // 批量插入网存开机日志
                if (!netStorageResOffLogList.isEmpty()) {
                    netStorageResOffLogService.saveBatch(netStorageResOffLogList);
                    log.info("批量插入网存开机日志，数量：{}", netStorageResOffLogList.size());
                }

                // 批量更新Pad的IP信息
                if (!padUpdateList.isEmpty()) {
                    // 单个刷新
                    padUpdateList.forEach(pad -> {
                        padMapper.updateById(pad);
                    });
                    log.info("批量更新Pad IP信息，数量：{}", padUpdateList.size());
                }

                // 批量插入算力关联关系
                if (!computeRelationList.isEmpty()) {
                    computeRelationMapper.batchInsert(computeRelationList);
                    log.info("批量插入算力关联关系，数量：{}", computeRelationList.size());
                    // 批量更新算力单元状态
                    netStorageComputeUnitService.updateBatchBindFlagByCodeList(
                            computeRelationList.stream().map(NetPadComputeUnitRelationDO::getNetStorageComputeUnitCode).collect(Collectors.toList()), 1);
                }

                // 更新设备分配状态
                if (!devicePadList.isEmpty()) {
                    List<Long> deviceIds = devicePadList.stream()
                            .map(DevicePad::getDeviceId)
                            .distinct()
                            .collect(Collectors.toList());
                    deviceMapper.updatePadAllocationStatusById(deviceIds, 2);
                    log.info("批量更新设备分配状态，设备数量：{}", deviceIds.size());
                }

                return null;
            } catch (Exception e) {
                log.error("批量保存初始化数据失败", e);
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    @Override
    public NetPadV2ResultVO batchBootOff(NetPadV2BatchOffDTO param, SourceTargetEnum sourceTargetEnum) {
        log.info("批量关机网存实例V2请求，参数：{}", param);
        validator.validateBatchOffParams(param);
        NetPadV2ResultVO result = new NetPadV2ResultVO();
        List<String> padCodes = new ArrayList<>(param.getPadCodes());
        PadTaskBO padTaskBO;
        List<PadDetailsVO> padDetailsList = getPadDetails(padCodes);
        // 通用校验
        Map<ExceptionCode, List<String>> commonValidateFailMap = validatePadListCommon(padDetailsList, padCodes, param.getCustomerId());
        if (!commonValidateFailMap.isEmpty()) {
            result.setFailList(commonValidateFailMap);
            padCodes.removeAll(result.getFailCodeList());
        }
        if (padCodes.isEmpty()) {
            return result;
        }
        // 状态校验（运行中 or 关机失败可以继续关机）
        if (!Objects.equals(sourceTargetEnum, SourceTargetEnum.ADMIN_SYSTEM)) {
            // 内部调用不校验状态
            Map<ExceptionCode, List<String>> validateOffStatusFailMap = validatePadOFFStatus(padDetailsList);
            if (!validateOffStatusFailMap.isEmpty()) {
                result.setFailList(validateOffStatusFailMap);
                padCodes.removeAll(result.getFailCodeList());
            }
            if (padCodes.isEmpty()) {
                return result;
            }
        }
        // 校验是否存在算力
        Map<ExceptionCode, List<String>> computeUnitNotExistFailMap = validateComputeUnitNotExist(padCodes);
        if (!computeUnitNotExistFailMap.isEmpty()) {
            result.setFailList(computeUnitNotExistFailMap);
            padCodes.removeAll(result.getFailCodeList());
        }
        if (padCodes.isEmpty()) {
            return result;
        }
        // 校验是否存在连续三次关机失败的任务
        Map<ExceptionCode, List<String>> continuousShutdownFailureFailMap = validateContinuousShutdownFailure(padCodes);
        if (!continuousShutdownFailureFailMap.isEmpty()) {
            result.setFailList(continuousShutdownFailureFailMap);
            padCodes.removeAll(result.getFailCodeList());
        }
        if (padCodes.isEmpty()) {
            return result;
        }
        List<String> padOffLockPadCodes = new ArrayList<>();
        List<String> padOnLockPadCodes = new ArrayList<>();
        try {
            // 锁定实例不允许同时操作开机关机
            // 批量锁定本次开机的全部实例1分钟，避免重复开关机。方法执行完成后释放。
            lockInstancesAndHandleFailures(padCodes, TaskTypeConstants.NET_PAD_OFF, TaskTypeConstants.NET_PAD_OFF, result);
            if (padCodes.isEmpty()) {
                return result;
            }
            padOffLockPadCodes = new ArrayList<>(padCodes);
            lockInstancesAndHandleFailures(padCodes, TaskTypeConstants.NET_PAD_ON, TaskTypeConstants.NET_PAD_OFF, result);
            if (padCodes.isEmpty()) {
                return result;
            }
            padOnLockPadCodes = new ArrayList<>(padCodes);
            // 上锁成功创建关机任务
            List<PadOffRequestDTO> bootOffRequestDTOS = new ArrayList<>();
            padDetailsList.forEach(padDetailsVO -> {
                PadOffRequestDTO bootOffRequestDTO = new PadOffRequestDTO();
                bootOffRequestDTO.setPadCode(padDetailsVO.getPadCode());
                bootOffRequestDTO.setDeviceIp(padDetailsVO.getDeviceIp());
                bootOffRequestDTO.setNetStorageResUnitCode(padDetailsVO.getNetStorageResId());
                bootOffRequestDTOS.add(bootOffRequestDTO);
            });
            cacheTaskRequest(bootOffRequestDTOS, TaskTypeConstants.NET_PAD_OFF);
            padTaskBO = padTaskComponent.addTimeoutPadTask(param.getCustomerId(), padCodes, NET_PAD_OFF,
                    null,  JSON.toJSONString(bootOffRequestDTOS), sourceTargetEnum, param.getTimeout());
            result.setSuccessList(padTaskBO);
            // 更新实例状态为关机中
            padStatusService.updatePadStatusAndSendPadStatusCallback(padCodes, PadStatusConstant.OFF_RUN, param.getCustomerId(), "batchBootOff");
        } finally {
            // 解锁实例
            unlockPadCodes(padOffLockPadCodes, TaskTypeConstants.NET_PAD_OFF);
            unlockPadCodes(padOnLockPadCodes, TaskTypeConstants.NET_PAD_ON);
        }
        return result;
    }

    @Override
    public void padOffHandler(String padCode) {
        Pad pad = padMapper.getByPadCode(padCode);
        if (pad == null) {
            log.error("padOffHandler pad is null, padCode: {}", padCode);
            return;
        }
        // 释放IP、板卡、算力
        unbindRelation(pad);
    }

    @Override
    public void padDelHandler(String padCode) {
        Pad pad = padMapper.getByPadCode(padCode);
        if (pad == null) {
            log.error("padOffHandler pad is null, padCode: {}", padCode);
            return;
        }
        transactionTemplate.execute(status -> {
            try {
                // 更新实例状态为删除
                pad.setStatus(-1);
                padMapper.updateById(pad);
                padStatusMapper.updatePadStatus(padCode, -1);
                // 移除存储资源绑定关系
                NetPadResUnitRelationDO relationDO = netPadStorageRelationMapper.getByPadCode(padCode);
                if (relationDO != null) {
                    netPadStorageRelationMapper.deleteById(relationDO.getId());
                    String netStorageResUnitCode = relationDO.getNetStorageResUnitCode();
                    // 逻辑删除
                    netStoragePadHelper.processNetStorageResUnitDeleteCallback(netStorageResUnitCode);
                }
                padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(pad.getPadCode()), DELETED, pad.getCustomerId(), "padDelHandler");
                return null;
            } catch (Exception e) {
                log.error("padOffHandler error", e);
                status.setRollbackOnly();
                return null;
            }
        });

    }

    @Override
    public NetPadV2ResultVO batchDelete(NetPadV2BatchDelDTO param, SourceTargetEnum sourceTargetEnum) {
        log.info("批量删除网存实例V2请求，参数：{}", param);
        validator.validateBatchDeleteParams(param);
        NetPadV2ResultVO result = new NetPadV2ResultVO();
        List<String> padCodes = new ArrayList<>(param.getPadCodes());
        PadTaskBO padTaskBO;
        List<String> padDelLockPadCodes = new ArrayList<>();
        List<String> padOnLockPadCodes = new ArrayList<>();
        try {
            // 删除时不允许操作删除与开机
            // 1. 锁定实例不允许同时操作删除与开机
            lockInstancesAndHandleFailures(padCodes, TaskTypeConstants.NET_PAD_DEL, TaskTypeConstants.NET_PAD_DEL, result);
            if (padCodes.isEmpty()) {
                return result;
            }
            padDelLockPadCodes = new ArrayList<>(padCodes);
            lockInstancesAndHandleFailures(padCodes, TaskTypeConstants.NET_PAD_ON, TaskTypeConstants.NET_PAD_DEL, result);
            if (padCodes.isEmpty()) {
                return result;
            }
            padOnLockPadCodes = new ArrayList<>(padCodes);
            List<PadDetailsVO> padDetails = getPadDetails(param.getPadCodes());
            Map<ExceptionCode, List<String>> validateFailMap = validatePadListCommon(padDetails, param.getPadCodes(), param.getCustomerId());
            if (!validateFailMap.isEmpty()) {
                result.setFailList(validateFailMap);
                padCodes.removeAll(result.getFailCodeList());
            }
            if (padCodes.isEmpty()) {
                return result;
            }
            // 校验实例状态，非关机、删除失败状态不能删除
            List<String> nonOffPadList = padDetails.stream()
                    .filter(padDetailsVO -> !Arrays.asList(DELETE_FAIL, OFF).contains(padDetailsVO.getPadStatus()))
                    .map(PadDetailsVO::getPadCode)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(nonOffPadList)) {
                result.setFailList(PadExceptionCode.PAD_CANT_DELETE, nonOffPadList);
                padCodes.removeAll(nonOffPadList);
            }
            if (padCodes.isEmpty()) {
                return result;
            }
            // 获取关联的网存存储
            List<NetPadResUnitRelationDO> relationDOList = netPadStorageRelationMapper.selectByPadCodes(padCodes);
            Map<String, NetPadResUnitRelationDO> resUnitRelationMap = relationDOList.stream().collect(Collectors.toMap(NetPadResUnitRelationDO::getPadCode, o -> o));
            // 创建删除任务参数
            List<NetStorageResOffLog> offLogList = netStorageResOffLogService.getByPadCodeList(padCodes);
            // offLogList转map,如果重复，则使用新的
            Map<String, String> offLogMap = new HashMap<>();
            offLogList.forEach(offLog -> {
                offLogMap.put(offLog.getPadCode(), offLog.getDeviceIp());
            });
            List<String> needAddTaskPadCodes = new ArrayList<>();
            List<PadDelRequestDTO> padDelRequestDTOList = new ArrayList<>();
            padDetails.forEach(padDetailsVO -> {
                PadDelRequestDTO padDelRequestDTO = new PadDelRequestDTO();
                if (offLogMap.get(padDetailsVO.getPadCode()) != null) {
                    padDelRequestDTO.setDeviceIp(offLogMap.get(padDetailsVO.getPadCode()));
                    needAddTaskPadCodes.add(padDetailsVO.getPadCode());
                } else {
                    // 没开过机直接走删除逻辑
                    padDelHandler(padDetailsVO.getPadCode());
                    result.setCompleteList(Collections.singletonList(padDetailsVO.getPadCode()));
                    return;
                }
                NetPadResUnitRelationDO netPadResUnitRelationDO = resUnitRelationMap.get(padDetailsVO.getPadCode());
                if (netPadResUnitRelationDO != null) {
                    padDelRequestDTO.setNetStorageResUnitCode(netPadResUnitRelationDO.getNetStorageResUnitCode());
                }
                padDelRequestDTO.setPadCode(padDetailsVO.getPadCode());
                padDelRequestDTO.setClusterCode(padDetailsVO.getClusterCode());
                padDelRequestDTOList.add(padDelRequestDTO);
            });
            if (!CollectionUtils.isEmpty(needAddTaskPadCodes)) {
                cacheTaskRequest(padDelRequestDTOList, NET_PAD_DEL);
                // 添加删除任务
                padTaskBO = padTaskComponent.addTimeoutPadTask(param.getCustomerId(), needAddTaskPadCodes, NET_PAD_DEL,
                        null,  JSON.toJSONString(padDelRequestDTOList), sourceTargetEnum, param.getTimeout());
                result.setSuccessList(padTaskBO);
            }
            // 更新实例状态为删除中
            padStatusService.updatePadStatusAndSendPadStatusCallback(padCodes, PadStatusConstant.DELETING, param.getCustomerId(), "batchDelete");
        } finally {
            // 解锁
            unlockPadCodes(padDelLockPadCodes, NET_PAD_DEL);
            unlockPadCodes(padOnLockPadCodes, NET_PAD_ON);
        }
        return result;
    }

    private void unbindRelation(Pad pad) {
        // 释放IP和服务器的关系
        boolean result = Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            try {
                LambdaUpdateWrapper<Pad> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Pad::getPadCode, pad.getPadCode());
                updateWrapper.set(Pad::getPadIp, null);
                updateWrapper.set(Pad::getArmServerCode, null);
                padMapper.update(null, updateWrapper);
                // 释放板卡
                DevicePad devicePad = devicePadMapper.selectByPadId(pad.getId());
                if (devicePad != null) {
                    devicePadMapper.deleteById(devicePad);
                    // 检查板卡是否存在其他绑定关系，如果存在则修改板卡分配状态
                    List<DevicePad> devicePads = devicePadMapper.selectList(devicePad.getDeviceId());
                    if (CollectionUtils.isEmpty(devicePads)) {
                        deviceMapper.updatePadAllocationStatusById(Collections.singletonList(devicePad.getDeviceId()), PadAllocationStatusConstants.UNALLOCATED.getStatus());
                    }
                } else {
                    log.warn("未找到实例与板卡的绑定关系，padCode: {}", pad.getPadCode());
                }
                // 释放算力
                NetPadComputeUnitRelationDO netPadComputeUnitRelationDO = computeRelationMapper.selectByPadCode(pad.getPadCode());
                if (netPadComputeUnitRelationDO != null) {
                    computeRelationMapper.deleteById(netPadComputeUnitRelationDO.getId());
                    netStorageComputeUnitService.updateBatchBindFlagByCodeList(Collections.singletonList(netPadComputeUnitRelationDO.getNetStorageComputeUnitCode()), 0);
                } else {
                    log.warn("未找到实例与算力的绑定关系，padCode: {}", pad.getPadCode());
                }
                return true;
            } catch (Exception e) {
                log.error("padOffHandler error", e);
                status.setRollbackOnly();
                // 修改实例状态为异常
                padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(pad.getPadCode()), PadStatusConstant.ABNORMAL, pad.getCustomerId(), "padOffHandler");
                return false;
            }
        }));
        if (result) {
            // 更新实例状态为关机
            padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(pad.getPadCode()), OFF, pad.getCustomerId(), "padOffHandler");
        }

    }

    private Map<ExceptionCode, List<String>> validateContinuousShutdownFailure(List<String> padCodes) {
        Map<ExceptionCode, List<String>> resultMap = new HashMap<>();
        List<String> failedPadCodes = new ArrayList<>();
        padCodes.forEach(padCode -> {
            List<PadTask> lastNCompletedTasks = padTaskMapper.getLastNCompletedTasks(padCode);
            // 判断3个任务均为关机任务，并且都是失败状态。
            boolean isValid = lastNCompletedTasks.size() >= 3 &&
                    lastNCompletedTasks.stream()
                            .allMatch(task ->
                                    Objects.equals(NET_PAD_OFF.getType(),task.getType()) && !Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus(), TaskStatusConstants.SUCCESS.getStatus()).contains(task.getStatus()));
            if (isValid) {
                failedPadCodes.add(padCode);
                String message = String.format("网存实例连续关机失败,请检查实例详情. padCode: %s", padCode);
                DingTalkRobotClient.sendMessage(springProfilesActive, message);
            }
        });
        if (!failedPadCodes.isEmpty()) {
            resultMap.put(PadExceptionCode.NET_PAD_CONTINUOUS_SHUTDOWN_FAILURE, failedPadCodes);
        }
        return resultMap;
    }

    /**
     * 校验实例列表是否满足通用校验
     */
    private Map<ExceptionCode, List<String>> validatePadListCommon(List<PadDetailsVO> detailsVOList, List<String> padCodes, Long customerId) {

        Map<ExceptionCode, List<String>> resultMap = new HashMap<>();

        // 校验全部实例code是否存在
        if (detailsVOList.size() != padCodes.size()) {
            List<String> notExistPadCodes = padCodes.stream().filter(padCode -> detailsVOList.stream().noneMatch(vo -> vo.getPadCode().equals(padCode))).collect(Collectors.toList());
            resultMap.put(PadExceptionCode.PAD_CODE_NOT_EXIST, notExistPadCodes);
        }

        // 检查是否存在非网存实例
        List<String> nonNetStoragePadCodes = detailsVOList.stream().filter(padDetailsVO -> padDetailsVO.getNetStorageResFlag() == 0).map(PadDetailsVO::getPadCode).collect(Collectors.toList());
        if (!nonNetStoragePadCodes.isEmpty()) {
            resultMap.put(PadExceptionCode.PAD_IS_NOT_NET_STORAGE, nonNetStoragePadCodes);
        }

        // 检查操作的实例是否在同一个用户下
        List<String> notBelongToCustomerPadCodes = detailsVOList.stream().filter(vo -> !Objects.equals(vo.getCustomerId(), customerId)).map(PadDetailsVO::getPadCode).collect(Collectors.toList());
        // 检查实例是否属于当前用户
        if (!CollectionUtil.isEmpty(notBelongToCustomerPadCodes)) {
            if (redisService.isAdmin(customerId)) {
                return resultMap;
            }
            resultMap.put(PadExceptionCode.PAD_CODE_NOT_BELONG_TO_CUSTOMER, padCodes);
        }
        return resultMap;
    }

    /**
     * 校验实例状态是否为可开机的状态
     */
    private Map<ExceptionCode, List<String>> validatePadONStatus(List<PadDetailsVO> detailsVOList) {
        Map<ExceptionCode, List<String>> resultMap = new HashMap<>();
        // 获取全部非关机/开机失败状态的实例
        List<PadDetailsVO> nonOffPadList = detailsVOList.stream()
                .filter(padDetailsVO -> !Arrays.asList(OFF, PadStatusConstant.ON_ERROR).contains(padDetailsVO.getPadStatus()))
                .collect(Collectors.toList());
        if (!nonOffPadList.isEmpty()) {
            resultMap.put(PadExceptionCode.PAD_IS_NOT_OFF, nonOffPadList.stream().map(PadDetailsVO::getPadCode).collect(Collectors.toList()));
        }
        return resultMap;
    }

    /**
     * 校验实例状态是否为可关机的状态
     */
    private Map<ExceptionCode, List<String>> validatePadOFFStatus(List<PadDetailsVO> detailsVOList) {
        Map<ExceptionCode, List<String>> resultMap = new HashMap<>();
        // 只有运行中或关机失败可以继续关机
        List<PadDetailsVO> nonOffPadList = detailsVOList.stream()
                .filter(padDetailsVO -> !Arrays.asList(PadStatusConstant.RUNNING, PadStatusConstant.OFF_ERROR).contains(padDetailsVO.getPadStatus()))
                .collect(Collectors.toList());
        if (!nonOffPadList.isEmpty()) {
            resultMap.put(PadExceptionCode.PAD_CANT_OFF, nonOffPadList.stream().map(PadDetailsVO::getPadCode).collect(Collectors.toList()));
        }
        return resultMap;
    }

    private List<PadDetailsVO> getPadDetails(List<String> padCodeList) {
        PadDetailsDTO dto = new PadDetailsDTO();
        dto.setPadCodes(padCodeList);
        return padMapper.selectDetailsByPadCode(dto);
    }

    /**
     * 创建开机任务
     */
    private PadTaskBO createBootOnTasks(NetPadV2BatchBootOnDTO param,
                                                     List<String> padCodes,
                                                     String taskParamJsonStr,
                                                     SourceTargetEnum sourceTargetEnum) {

        // 创建任务
        return padTaskComponent.addTimeoutPadTask(
                param.getCustomerId(),
                padCodes,
                TaskTypeConstants.NET_PAD_ON,
                null,
                taskParamJsonStr,
                sourceTargetEnum,
                param.getTimeout()
        );
    }


    private void batchInsertStorageRelations(List<NetPadResUnitRelationDO> storageRelations) {
        if (!storageRelations.isEmpty()) {
            // 分批插入，避免单次插入数据过多
            int batchSize = 100;
            for (int i = 0; i < storageRelations.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, storageRelations.size());
                List<NetPadResUnitRelationDO> batch = storageRelations.subList(i, endIndex);
                storageRelationMapper.batchInsert(batch);
            }
        }
    }

    /**
     * 实例创建数据内部类
     */
    private static class InstanceCreationData {
        String padCode;
        String netStorageResUnitCode;
        String dns;
        int padSn;
        Long businessSnowflakeId;
    }

    /**
     * 生成网存实例编码
     * 使用雪花算法生成全局唯一ID，确保不会冲突
     */
    private String generateNetPadCode() {
        // 直接使用雪花算法生成的完整ID，确保全局唯一
        long snowflakeId = SnowflakeIdGeneratorV3.nextIdLong();
        return "ACN" + snowflakeId;
    }

    /**
     * 生成网存单元编码
     * 使用雪花算法生成全局唯一ID，确保不会冲突
     */
    private String generateNetStorageResUnitCode(String padCode) {
        // 直接使用雪花算法生成的完整ID，确保全局唯一
        long snowflakeId = SnowflakeIdGeneratorV3.nextIdLong();
        return "ZSC" + snowflakeId + "-" + padCode;
    }

    /**
     * 获取创建实例时传入的参数
     *
     * @param padCode 实例编码
     * @return 创建实例时的参数信息
     */
    private NetPadV2CreateParamsVO getInstanceCreationParams(String padCode) {
        // 查询Pad表获取基本参数
        QueryWrapper<Pad> padQuery = new QueryWrapper<>();
        padQuery.eq("pad_code", padCode);
        Pad pad = padMapper.selectOne(padQuery);

        if (pad == null) {
            log.warn("未找到实例信息，padCode: {}", padCode);
            return null;
        }

        // 查询NetStoragePadUnitDetail表获取详细参数
        QueryWrapper<NetStoragePadUnitDetail> detailQuery = new QueryWrapper<>();
        detailQuery.eq("pad_code", padCode);
        NetStoragePadUnitDetail detail = netStoragePadUnitDetailMapper.selectOne(detailQuery);

        // 封装参数信息
        NetPadV2CreateParamsVO params = new NetPadV2CreateParamsVO();
        params.setPadCode(padCode);
        params.setDns(pad.getDns());
        params.setCountryCode(pad.getCountryCode());

        if (detail != null) {
            // 解析androidProp JSON字符串
            if (StringUtils.isNotBlank(detail.getDeviceAndroidProp())) {
                try {
                    JSONObject androidProp = JSONObject.parseObject(detail.getDeviceAndroidProp());
                    params.setAndroidProp(androidProp);
                } catch (Exception e) {
                    log.warn("解析androidProp失败，padCode: {}, androidProp: {}", padCode, detail.getDeviceAndroidProp(), e);
                }
            }
        }

        return params;
    }

    /**
     * 对比创建实例参数与开机参数
     *
     * @param bootOnParam 开机参数
     * @param creationParams 创建实例参数
     * @return 参数对比结果
     */
    private ParamCompareResultVO compareInstanceParams(NetPadV2BatchBootOnDTO bootOnParam, NetPadV2CreateParamsVO creationParams) {
        ParamCompareResultVO result = new ParamCompareResultVO();
        result.setPadCode(creationParams.getPadCode());

        // 对比DNS配置
        if (StringUtils.isNotBlank(bootOnParam.getDns()) &&
            !Objects.equals(bootOnParam.getDns(), creationParams.getDns())) {
            result.setDnsChanged(true);
            result.setNewDns(bootOnParam.getDns());
            result.setNeedUpdate(true);
        }

        // 对比CountryCode配置
        if (StringUtils.isNotBlank(bootOnParam.getCountryCode()) &&
            !Objects.equals(bootOnParam.getCountryCode(), creationParams.getCountryCode())) {
            result.setCountryCodeChanged(true);
            result.setNewCountryCode(bootOnParam.getCountryCode());
            result.setNeedUpdate(true);
        }

        // 对比安卓属性
        if (bootOnParam.getAndroidProp() != null && !bootOnParam.getAndroidProp().isEmpty()) {
            // 如果开机传入了androidProp，需要与创建时的进行对比
            if (creationParams.getAndroidProp() == null ||
                !bootOnParam.getAndroidProp().equals(creationParams.getAndroidProp())) {
                result.setAndroidPropChanged(true);
                // 合并androidProp，使用bootOnParam覆盖合并creationParams
                if (creationParams.getAndroidProp() != null) {
                    // 创建新的JSONObject保存合并结果
                    JSONObject mergedProps = new JSONObject();
                    // 先添加创建时的参数
                    mergedProps.putAll(creationParams.getAndroidProp());
                    // 再添加开机参数，这样开机参数会覆盖创建时的同名参数
                    mergedProps.putAll(bootOnParam.getAndroidProp());
                    // 更新bootOnParam中的androidProp
                    bootOnParam.setAndroidProp(mergedProps);
                }
                result.setNewAndroidProp(bootOnParam.getAndroidProp());
                result.setNeedUpdate(true);
            }
        }

        return result;
    }

    /**
     * 对比创建实例参数与开机参数，如有不一致则更新
     *
     * @param bootOnParam 开机参数
     * @param detailsVOList 实例详情列表
     */
    private boolean compareAndUpdateInstanceParams(NetPadV2BatchBootOnDTO bootOnParam, List<PadDetailsVO> detailsVOList) {
        List<ParamCompareResultVO> compareResults = new ArrayList<>();

        // 遍历每个实例进行参数对比
        for (PadDetailsVO detailsVO : detailsVOList) {
            String padCode = detailsVO.getPadCode();

            // 获取创建实例时的参数
            NetPadV2CreateParamsVO creationParams = getInstanceCreationParams(padCode);
            if (creationParams == null) {
                log.warn("无法获取实例创建参数，跳过参数对比，padCode: {}", padCode);
                continue;
            }

            // 对比参数
            ParamCompareResultVO compareResult = compareInstanceParams(bootOnParam, creationParams);
            if (compareResult.isNeedUpdate()) {
                compareResults.add(compareResult);
                log.info("实例参数需要更新，padCode: {}, dnsChanged: {}, countryCodeChanged: {}, androidPropChanged: {}",
                    padCode, compareResult.isDnsChanged(), compareResult.isCountryCodeChanged(), compareResult.isAndroidPropChanged());
            } else {
                // 不需要更新时，判断开机传入的参数中是否为空，如果为空则使用创建时传入的参数覆盖
                if (bootOnParam.getAndroidProp() == null || bootOnParam.getAndroidProp().isEmpty()) {
                    bootOnParam.setAndroidProp(creationParams.getAndroidProp());
                }
                if (StringUtils.isBlank(bootOnParam.getDns())) {
                    bootOnParam.setDns(creationParams.getDns());
                }
                if (StringUtils.isBlank(bootOnParam.getCountryCode())) {
                    bootOnParam.setCountryCode(creationParams.getCountryCode());
                }
            }
        }

        // 批量更新参数
        if (!compareResults.isEmpty()) {
            batchUpdateInstanceParams(compareResults);
            log.info("批量更新实例参数完成，更新数量: {}", compareResults.size());
            return true;
        } else {
            log.info("所有实例参数一致，无需更新");
            return false;
        }
    }

    /**
     * 批量更新实例参数
     *
     * @param compareResults 参数对比结果列表
     */
    private void batchUpdateInstanceParams(List<ParamCompareResultVO> compareResults) {
        // 使用事务模板确保数据一致性
        try {
            // 分别处理需要更新Pad表和NetStoragePadUnitDetail表的数据
            List<Pad> padsToUpdate = new ArrayList<>();
            List<Pad> padsCountryCodeToUpdate = new ArrayList<>();
            List<NetStoragePadUnitDetail> detailsToUpdate = new ArrayList<>();

            for (ParamCompareResultVO result : compareResults) {
                // 更新Pad表的DNS字段
                if (result.isDnsChanged()) {
                    Pad pad = new Pad();
                    pad.setPadCode(result.getPadCode());
                    pad.setDns(result.getNewDns());
                    padsToUpdate.add(pad);
                }

                if (result.isCountryCodeChanged()) {
                    Pad pad = new Pad();
                    pad.setPadCode(result.getPadCode());
                    pad.setCountryCode(result.getNewCountryCode());
                    padsCountryCodeToUpdate.add(pad);
                }

                // 更新NetStoragePadUnitDetail表的androidProp字段
                if (result.isAndroidPropChanged()) {
                    // 查询现有的detail记录
                    QueryWrapper<NetStoragePadUnitDetail> query = new QueryWrapper<>();
                    query.eq("pad_code", result.getPadCode());
                    NetStoragePadUnitDetail detail = netStoragePadUnitDetailMapper.selectOne(query);

                    if (detail != null) {
                        JSONObject newAndroidProp = result.getNewAndroidProp();
                        detail.setDeviceAndroidProp(newAndroidProp.toJSONString());
                        detailsToUpdate.add(detail);
                    }
                }
            }

            // 批量更新Pad表
            if (!padsToUpdate.isEmpty()) {
                for (Pad pad : padsToUpdate) {
                    UpdateWrapper<Pad> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("pad_code", pad.getPadCode());
                    updateWrapper.set("dns", pad.getDns());
                    updateWrapper.set("update_time", new Date());
                    padMapper.update(null, updateWrapper);
                }
                log.info("批量更新Pad表DNS字段完成，更新数量: {}", padsToUpdate.size());
            }

            if (!padsCountryCodeToUpdate.isEmpty()) {
                for (Pad pad : padsCountryCodeToUpdate) {
                    UpdateWrapper<Pad> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("pad_code", pad.getPadCode());
                    updateWrapper.set("country_code", pad.getCountryCode());
                    updateWrapper.set("update_time", new Date());
                    padMapper.update(null, updateWrapper);
                }
                log.info("批量更新Pad表countryCode字段完成，更新数量: {}", padsCountryCodeToUpdate.size());
            }

            // 批量更新NetStoragePadUnitDetail表
            if (!detailsToUpdate.isEmpty()) {
                for (NetStoragePadUnitDetail detail : detailsToUpdate) {
                    UpdateWrapper<NetStoragePadUnitDetail> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("pad_code", detail.getPadCode());
                    updateWrapper.set("device_android_prop", detail.getDeviceAndroidProp());
                    netStoragePadUnitDetailMapper.update(null, updateWrapper);
                }
                log.info("批量更新NetStoragePadUnitDetail表androidProp字段完成，更新数量: {}", detailsToUpdate.size());
            }
        } catch (Exception e) {
            log.error("批量更新实例参数失败", e);
            throw e;
        }
    }

    /**
     * 获取DNS配置
     */
    private String getDnsConfig(NetPadV2CreateDTO dto) {
        String dns = dto.getDns();
        if (StrUtil.isEmpty(dns)) {
            String value = queryService.getEdgeClusterConfigurationByKey(
                    dto.getClusterCode(), EdgeClusterConfigurationEnum.CLUSTER_DNS_DEFAULT_SERVERS);
            if (StrUtil.isNotBlank(value)) {
                dns = value;
            }
        }
        return dns;
    }

    /**
     * 创建实例状态记录实体
     */
    private PadStatus createPadStatusEntity(String padCode) {
        PadStatus padStatus = new PadStatus();
        padStatus.setPadCode(padCode);
        padStatus.setPadOutCode(padCode);
        padStatus.setPadStatus(OFF); // 网存实例创建默认关机
        return padStatus;
    }

    /**
     * 批量插入Pad实例 - 使用SQL层面的批量插入
     */
    private void batchInsertPads(List<Pad> pads) {
        if (!pads.isEmpty()) {
            // 分批插入，避免单次插入数据过多
            int batchSize = 100;
            for (int i = 0; i < pads.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, pads.size());
                List<Pad> batch = pads.subList(i, endIndex);
                padMapper.batchInsertPads(batch);
            }
        }
    }

    /**
     * 批量插入网存单元
     */
    private void batchInsertNetStorageResUnits(List<NetStorageResUnit> units) {
        if (!units.isEmpty()) {
            netStorageResUnitService.saveBatch(units);
        }
    }

    /**
     * 批量插入实例状态
     */
    private void batchInsertPadStatuses(List<PadStatus> statuses) {
        if (!statuses.isEmpty()) {
            // 分批插入，避免单次插入数据过多
            int batchSize = 100;
            for (int i = 0; i < statuses.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, statuses.size());
                List<PadStatus> batch = statuses.subList(i, endIndex);
                padStatusMapper.batchInsertPadStatus(batch);
            }
        }
    }

    /**
     * 批量插入房间
     */
    private void batchInsertPadRooms(List<PadRoom> rooms) {
        if (!rooms.isEmpty()) {
            // 分批插入，避免单次插入数据过多
            int batchSize = 100;
            for (int i = 0; i < rooms.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, rooms.size());
                List<PadRoom> batch = rooms.subList(i, endIndex);
                padRoomMapper.batchInsertPadRooms(batch);
            }
        }
    }

    /**
     * 批量插入网存实例详情
     */
    private void batchInsertNetStoragePadUnitDetails(List<NetStoragePadUnitDetail> details) {
        if (!details.isEmpty()) {
            // 分批插入，避免单次插入数据过多
            int batchSize = 100;
            for (int i = 0; i < details.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, details.size());
                List<NetStoragePadUnitDetail> batch = details.subList(i, endIndex);
                netStoragePadUnitDetailMapper.batchInsertNetStoragePadUnitDetails(batch);
            }
        }
    }

    /**
     * 创建简化记录
     */
    private NetPadV2CreateRecord createSimpleRecord(String padCode) {
        NetPadV2CreateRecord record = new NetPadV2CreateRecord();
        record.setPadCode(padCode);
        record.setCreateTime(new Date());
        return record;
    }

    /**
     * 创建房间实体
     */
    private PadRoom createPadRoomEntity(String padCode, Integer type, String roomType, String oprBy) {
        PadRoom room = new PadRoom();
        room.setPadCode(padCode);
        room.setStatus(1);
        room.setType(type);
        room.setCreateBy("netpadv2:" + oprBy);
        room.setCreateTime(new Date());

        // 根据类型生成房间编码
        if (CRM.equals(roomType)) {
            room.setRoomCode(padCode.replace(AC, CRM));
        } else if (SRM.equals(roomType)) {
            room.setRoomCode(padCode.replace(AC, SRM));
        }

        return room;
    }

    /**
     * 创建网存实例详情实体
     */
    private NetStoragePadUnitDetail createNetStoragePadUnitDetailEntity(NetPadV2CreateDTO dto, Pad pad, String netStorageResUnitCode) {
        NetStoragePadUnitDetail unitDetail = new NetStoragePadUnitDetail();
        unitDetail.setPadCode(pad.getPadCode());
        unitDetail.setClusterCode(pad.getClusterCode());
        unitDetail.setNetStorageResApplySize(String.valueOf(pad.getNetStorageResSize()));
        unitDetail.setRealPhoneTemplateId(pad.getRealPhoneTemplateId());
        unitDetail.setNetStorageResUnitCode(netStorageResUnitCode);
        unitDetail.setDeviceAndroidProp(dto.getAndroidProp() == null ? "" : dto.getAndroidProp().toJSONString());
        return unitDetail;
    }


    /**
     * 处理ADI模板
     */
    private RealPhoneTemplate handleRealPhoneTemplate(NetPadV2CreateDTO dto, CustomerUploadImage customerUploadImage) {
        if (dto.getRandomADITemplates()) {
            // 随机选择ADI模板
            return queryService.getRandomRealPhoneTemplate(customerUploadImage.getAndroidImageVersion());
        } else if (Objects.nonNull(dto.getRealPhoneTemplateId())) {
            // 使用指定的ADI模板
            return queryService.getRealPhoneTemplateById(dto.getRealPhoneTemplateId());
        }
        return null;
    }

    /**
     * 获取屏幕布局
     */
    private ScreenLayout getScreenLayout(String screenLayoutCode,
                                         Long customerId, SourceTargetEnum sourceTarget) {
        return queryService.getScreenLayoutByCodeAndCustomer(screenLayoutCode, customerId, sourceTarget);
    }

    public List<String> filterV2Pad(List<String> padCodes) {
        return recordMapper.getByPadCodeList(padCodes);
    }

}
