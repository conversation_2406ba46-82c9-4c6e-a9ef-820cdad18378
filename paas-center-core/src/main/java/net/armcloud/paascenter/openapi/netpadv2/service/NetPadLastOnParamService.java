package net.armcloud.paascenter.openapi.netpadv2.service;

import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadLastOnParam;

import java.util.Date;
import java.util.List;

/**
 * 网存实例开机参数记录服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
public interface NetPadLastOnParamService {

    /**
     * 记录网存实例开机参数
     * 在网存实例开机时调用，记录开机使用的设备、集群、服务器等信息
     * 
     * @param padCode 实例编码
     * @param deviceCode 板卡编号
     * @param deviceIp 板卡IP
     * @param clusterCode 集群编码
     * @param armServerCode 服务器编码
     */
    void recordBootOnParams(String padCode, String deviceCode, String deviceIp, 
                           String clusterCode, String armServerCode);

    /**
     * 批量记录网存实例开机参数
     * 
     * @param records 开机参数记录列表
     */
    void batchRecordBootOnParams(List<NetPadLastOnParam> records);

    /**
     * 更新开机成功状态和耗时
     * 在开机任务完成时调用，更新开机是否成功和开机耗时
     * 
     * @param padCode 实例编码
     * @param success 开机是否成功
     * @param startTime 开机开始时间
     */
    void updateBootOnResult(String padCode, boolean success, Date startTime);

    /**
     * 批量更新开机成功状态
     * 
     * @param padCodes 实例编码列表
     * @param success 开机是否成功
     */
    void batchUpdateBootOnResult(List<String> padCodes, boolean success);

    /**
     * 更新关机时间
     * 在网存实例关机时调用，记录关机时间
     * 
     * @param padCode 实例编码
     */
    void updateOffTime(String padCode);

    /**
     * 批量更新关机时间
     * 
     * @param padCodes 实例编码列表
     */
    void batchUpdateOffTime(List<String> padCodes);

    /**
     * 删除网存实例开机参数记录
     * 在删除网存实例时调用，同步删除开机参数记录
     * 
     * @param padCode 实例编码
     */
    void deleteByPadCode(String padCode);

    /**
     * 批量删除网存实例开机参数记录
     * 
     * @param padCodes 实例编码列表
     */
    void batchDeleteByPadCodes(List<String> padCodes);

    /**
     * 根据实例编码查询开机参数记录
     * 
     * @param padCode 实例编码
     * @return 开机参数记录
     */
    NetPadLastOnParam getByPadCode(String padCode);

    /**
     * 批量查询开机参数记录
     * 
     * @param padCodes 实例编码列表
     * @return 开机参数记录列表
     */
    List<NetPadLastOnParam> getByPadCodes(List<String> padCodes);
}
